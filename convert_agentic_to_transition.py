#!/usr/bin/env python3
"""
Script to convert agentic schema to transition schema using the complete conversion logic.
"""

import json
import sys
import os
from pathlib import Path

# Add the workflow-service app directory to the path
workflow_service_path = Path(__file__).parent / "workflow-service" / "app"
sys.path.insert(0, str(workflow_service_path))

try:
    from services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
    from services.workflow_builder.node_combiner import combine_nodes
except ImportError as e:
    print(f"❌ Error importing conversion modules: {e}")
    print(f"Make sure you're running this script from the backend directory")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Looking for modules in: {workflow_service_path}")
    sys.exit(1)


def load_agentic_schema(file_path: str) -> dict:
    """Load the agentic schema from JSON file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {file_path}: {e}")
        sys.exit(1)


def save_transition_schema(schema: dict, output_path: str) -> None:
    """Save the transition schema to JSON file."""
    try:
        with open(output_path, 'w') as f:
            json.dump(schema, f, indent=2)
        print(f"✅ Transition schema saved to: {output_path}")
    except Exception as e:
        print(f"❌ Error saving transition schema: {e}")
        sys.exit(1)


def main():
    """Main conversion function."""
    # Input and output file paths
    input_file = "agentic_schema.json"
    output_file = "transition_schema.json"
    
    print("🚀 AGENTIC TO TRANSITION SCHEMA CONVERTER")
    print("=" * 50)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        print(f"Please make sure {input_file} exists in the current directory")
        sys.exit(1)
    
    print(f"📂 Loading agentic schema from: {input_file}")
    agentic_schema = load_agentic_schema(input_file)
    
    # Extract workflow data from agentic schema
    workflow_data = agentic_schema.get("workflow_data", {})
    
    if not workflow_data:
        print("❌ No workflow_data found in agentic schema")
        sys.exit(1)
    
    print(f"📊 Agentic schema loaded successfully:")
    print(f"   - Workflow name: {agentic_schema.get('name', 'Unknown')}")
    print(f"   - Description: {agentic_schema.get('description', 'No description')}")
    print(f"   - Nodes: {len(workflow_data.get('nodes', []))}")
    print(f"   - Edges: {len(workflow_data.get('edges', []))}")
    
    try:
        print(f"\n🔄 Starting conversion process...")
        
        # Convert workflow data to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)
        
        print(f"\n✅ Conversion completed successfully!")
        print(f"📊 Transition schema summary:")
        print(f"   - Nodes: {len(transition_schema.get('nodes', []))}")
        print(f"   - Transitions: {len(transition_schema.get('transitions', []))}")
        
        # Save the result
        save_transition_schema(transition_schema, output_file)
        
        # Print some details about the converted schema
        print(f"\n📋 Conversion details:")
        
        # Show node types
        nodes = transition_schema.get('nodes', [])
        node_types = {}
        for node in nodes:
            server_script_path = node.get('server_script_path', 'unknown')
            if server_script_path not in node_types:
                node_types[server_script_path] = 0
            node_types[server_script_path] += 1
        
        print(f"   Node types:")
        for node_type, count in node_types.items():
            print(f"     - {node_type}: {count}")
        
        # Show transition types
        transitions = transition_schema.get('transitions', [])
        transition_types = {}
        execution_types = {}
        
        for transition in transitions:
            t_type = transition.get('transition_type', 'unknown')
            e_type = transition.get('execution_type', 'unknown')
            
            if t_type not in transition_types:
                transition_types[t_type] = 0
            transition_types[t_type] += 1
            
            if e_type not in execution_types:
                execution_types[e_type] = 0
            execution_types[e_type] += 1
        
        print(f"   Transition types:")
        for t_type, count in transition_types.items():
            print(f"     - {t_type}: {count}")
            
        print(f"   Execution types:")
        for e_type, count in execution_types.items():
            print(f"     - {e_type}: {count}")
        
        print(f"\n🎉 Conversion complete! Check {output_file} for the result.")
        
    except Exception as e:
        print(f"\n❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
