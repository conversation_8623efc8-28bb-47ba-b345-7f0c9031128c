from typing import Any, List, ClassVar, Dict
import importlib
import logging

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    DynamicHandleInput,
    DropdownInput,
    StringInput,
    IntInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.node_result import NodeResult
from app.models.workflow_builder.context import WorkflowContext
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.utils.tool_schema_generator import ToolSchemaGenerator
from app.constants.semantic_types import AI_RESPONSE, AI_METADATA, AI_MEMORY, ERROR_INFO


class AgenticAI(BaseAgentComponent):
    """
    Executes an AI agent with tools and memory using AutoGen.

    This component creates and runs an AutoGen agent that can use tools and maintain memory
    to accomplish a specified objective.
    """

    name: ClassVar[str] = "AgenticAI"
    display_name: ClassVar[str] = "AI Agent Executor"
    description: ClassVar[str] = "Executes an AI agent with tools and memory using AutoGen."

    icon: ClassVar[str] = "Bot"
    beta: ClassVar[bool] = True
    is_abstract: ClassVar[bool] = False  # Explicitly set to False to ensure it's not abstract

    # Use the component logger to log class loading
    from app.utils.workflow_builder.component_logger import log_component_loaded

    log_component_loaded("AgenticAI", "AI", is_abstract=False)

    # Define component-specific inputs using create_dual_purpose_input for unified inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Agent configuration (id and name are provided by platform)
        StringInput(
            name="description",
            display_name="Description",
            required=False,
            value="",
            info="Description of the agent for UI display.",
        ),

        # Execution configuration
        DropdownInput(
            name="execution_type",
            display_name="Execution Type",
            options=["response", "interactive"],
            value="response",
            info="Determines if agent handles single response or multi-turn conversation.",
        ),

        # Query/Objective - unified input that can be both connected and directly edited
        create_dual_purpose_input(
            name="query",
            display_name="Query/Objective",
            input_type="string",
            required=True,
            info="The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.",
            input_types=["string"],
        ),

        # System message configuration
        StringInput(
            name="system_message",
            display_name="System Message",
            required=False,
            value="",
            info="System prompt/instructions for the agent. If empty, will use default based on query.",
        ),

        # Termination condition (conditionally required for interactive mode)
        StringInput(
            name="termination_condition",
            display_name="Termination Condition",
            required=False,
            value="",
            info="Defines when multi-turn conversations should end. Required for interactive execution type.",
            visibility_rules=[
                InputVisibilityRule(field_name="execution_type", field_value="interactive"),
            ],
            visibility_logic="OR",
        ),

        # Max tokens configuration
        IntInput(
            name="max_tokens",
            display_name="Max Tokens",
            required=False,
            value=1000,
            info="Maximum response length in tokens.",
        ),

        # Input variables - unified input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_variables",
            display_name="Input Variables",
            input_type="dict",
            required=False,
            info="Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.",
            input_types=["dict"],
            value={},
        ),

        # Workflow Components (Tools) - single handle input for connecting multiple workflow components as tools
        HandleInput(
            name="tools",
            display_name="Tools",
            is_handle=True,
            input_types=["Any"],
            info="Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.",
            allow_direct_input=False,  # No manual JSON configuration
        ),

        # Memory - connection handle (keeping as handle-only since memory objects are typically created by other nodes)
        HandleInput(
            name="memory",
            display_name="Memory Object",
            is_handle=True,
            info="Connect a memory object from another node.",
        ),

        # AutoGen agent type - dropdown (for internal AutoGen configuration)
        DropdownInput(
            name="autogen_agent_type",
            display_name="AutoGen Agent Type",
            options=["Assistant", "UserProxy", "CodeExecutor"],
            value="Assistant",
            info="The type of AutoGen agent to create internally.",
            advanced=True,
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name", "temperature"]
    ] + component_inputs

    # Hardcoded data for orchestration engine compatibility
    @property
    def agent_type(self) -> str:
        """Return hardcoded agent_type as 'component' for orchestration engine."""
        return "component"

    def get_agent_config(self, context: WorkflowContext) -> Dict[str, Any]:
        """
        Generate agent configuration for the orchestration engine.

        Args:
            context: The workflow execution context.

        Returns:
            Dictionary containing agent configuration in orchestration engine format.
        """
        # Get configuration values (id and name are provided by platform)
        description = self.get_input_value("description", context, "")
        execution_type = self.get_input_value("execution_type", context, "response")
        query = self.get_input_value("query", context, "")
        system_message = self.get_input_value("system_message", context, "")
        termination_condition = self.get_input_value("termination_condition", context, "")
        max_tokens = self.get_input_value("max_tokens", context, 1000)

        # Extract connected workflow components as tools
        tools = self._extract_connected_workflow_components(context)

        # Get model configuration
        model_provider = self.get_input_value("model_provider", context, "OpenAI")
        model_name = self.get_input_value("model_name", context, "gpt-4-turbo")
        temperature = self.get_input_value("temperature", context, 0.7)

        # Use platform-provided node ID and component display name
        agent_id = f"agent_{context.current_node_id}" if context.current_node_id else "agent_unknown"
        agent_name = self.display_name  # Use component's display name

        # Get node name from config (populated by the transformation process) or fallback to context
        node_name = self.get_input_value("name", context, None)
        if not node_name:
            # Fallback to context node_outputs if not in config, or use agent_name
            node_name = context.node_outputs.get(context.current_node_id, {}).get("label", agent_name)

        # Get node id from config (populated by the transformation process) or fallback to context
        node_id = self.get_input_value("id", context, None)
        if not node_id:
            node_id = context.current_node_id

        # Build agent configuration
        agent_config = {
            "id": node_id or agent_id,  # Use the node_id from config if available
            "name": node_name,  # Use the user-configured name
            "description": description,
            "agent_type": self.agent_type,  # Always "component"
            "execution_type": execution_type,
            "query": query,
            "system_message": system_message,
            "tools": tools,
            "agent_config": {
                "model_provider": model_provider,
                "model_name": model_name,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
        }

        # Add termination condition only for interactive agents
        if execution_type == "interactive":
            agent_config["termination_condition"] = termination_condition

        return agent_config

    def _extract_connected_workflow_components(self, context: WorkflowContext) -> List[Dict[str, Any]]:
        """
        Extract connected workflow components from config.tools and convert them to tool format.

        Uses the simplified config.tools format (array of tool configurations).

        Args:
            context: The workflow execution context.

        Returns:
            List of tool configurations in agent_config format.
        """
        tools = []

        # Get the current node ID
        node_id = context.current_node_id
        if not node_id:
            return tools

        # Initialize tool schema generator
        schema_generator = ToolSchemaGenerator()

        # CRITICAL FIX: Check for execution tool data first (from schema converter)
        # This handles the case where tools are stored in config but need to be available for execution
        execution_tool_data = None
        if hasattr(context, 'current_node_config') and context.current_node_config:
            execution_tool_data = context.current_node_config.get("_execution_tool_data")

        if execution_tool_data:
            context.log(f"Found {len(execution_tool_data)} tools in execution tool data")
            tool_connections = execution_tool_data
        else:
            # Check for simplified format (config.tools)
            if hasattr(context, 'current_node_config') and context.current_node_config:
                simplified_tools = context.current_node_config.get("tools")
                if simplified_tools and isinstance(simplified_tools, list):
                    context.log(f"Found {len(simplified_tools)} tools in config.tools format")
                    tool_connections = simplified_tools
                else:
                    # Fallback: Look for tools in node inputs
                    node_inputs = context.node_outputs.get(node_id, {})
                    tools_input = node_inputs.get("tools")
                    if not tools_input:
                        context.log("No tools found in config.tools or node inputs")
                        return tools

                    # Handle both single tool and array of tools
                    tool_connections = tools_input if isinstance(tools_input, list) else [tools_input]
                    context.log(f"Found {len(tool_connections)} tools in node inputs")

        for tool_data in tool_connections:
            if tool_data:
                # Generate AutoGen-compatible tool schema if not already present
                if "component_schema" not in tool_data:
                    try:
                        # Generate schema using the universal tool schema generator
                        autogen_schema = schema_generator.generate_universal_schema(tool_data)
                        tool_data["component_schema"] = autogen_schema
                    except Exception as e:
                        # Log error but continue with other tools
                        context.log(f"Warning: Failed to generate schema for tool: {str(e)}")

                # Convert workflow component to tool format
                tool_config = {
                    "tool_type": "workflow_component",
                    "component": {
                        "component_id": tool_data.get("component_id", ""),
                        "component_type": tool_data.get("component_type", ""),
                        "component_name": tool_data.get("component_name", ""),
                    }
                }

                # Add optional fields if present
                if "component_schema" in tool_data:
                    tool_config["component"]["component_schema"] = tool_data["component_schema"]

                if "component_config" in tool_data:
                    tool_config["component"]["component_config"] = tool_data["component_config"]

                if "mcp_metadata" in tool_data:
                    tool_config["component"]["mcp_metadata"] = tool_data["mcp_metadata"]

                tools.append(tool_config)

        return tools

    outputs: ClassVar[List[Output]] = [
        Output(name="final_answer", display_name="Final Answer", output_type="string", semantic_type=AI_RESPONSE),
        Output(
            name="intermediate_steps",
            display_name="Intermediate Steps",
            output_type="list",
            semantic_type=AI_METADATA,
        ),
        Output(
            name="updated_memory", display_name="Updated Memory", output_type="Memory", semantic_type=AI_MEMORY
        ),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _check_autogen_installed(self) -> bool:
        """
        Checks if the autogen package is installed.

        Returns:
            True if autogen is installed, False otherwise.
        """
        try:
            importlib.import_module("autogen")
            return True
        except ImportError:
            return False



    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the AgenticAI component using AutoGen.

        This method creates and runs an AutoGen agent that can use tools and maintain memory
        to accomplish a specified objective.

        Args:
            context: The workflow execution context.

        Returns:
            A NodeResult with the execution results.
        """
        # Log execution start
        context.log(f"Executing {self.name}...")

        # Check if autogen is installed
        if not self._check_autogen_installed():
            error_msg = "The autogen package is required but not installed. Please install it with 'pip install pyautogen'."
            context.log(error_msg)
            return NodeResult.error(error_msg)

        # Get inputs from the context
        try:
            # Get model configuration
            model_provider = self.get_input_value("model_provider", context)
            base_url = self.get_input_value("base_url", context)
            api_key = self.get_input_value("api_key", context)
            model_name = self.get_input_value("model_name", context)
            temperature = self.get_input_value("temperature", context, 0.7)
            max_tokens = self.get_input_value("max_tokens", context, 1000)

            # Get agent configuration (id and name are provided by platform)
            description = self.get_input_value("description", context, "")
            execution_type = self.get_input_value("execution_type", context, "response")
            query = self.get_input_value("query", context)
            system_message = self.get_input_value("system_message", context, "")
            termination_condition = self.get_input_value("termination_condition", context, "")
            input_variables = self.get_input_value("input_variables", context, {})
            # Extract connected workflow components as tools
            tools = self._extract_connected_workflow_components(context)
            memory = self.get_input_value("memory", context)
            autogen_agent_type = self.get_input_value("autogen_agent_type", context, "Assistant")
            stream = self.get_input_value("stream", context, False)

            # Use platform-provided values for agent identity
            agent_name = self.display_name  # Use component's display name

            # Validate required inputs
            if not query:
                error_msg = "Query/Objective is required."
                context.log(error_msg)
                return NodeResult.error(error_msg)

            # Validate termination condition for interactive mode
            if execution_type == "interactive" and not termination_condition:
                error_msg = "Termination condition is required for interactive execution type."
                context.log(error_msg)
                return NodeResult.error(error_msg)

            if not api_key:
                error_msg = "API key is required."
                context.log(error_msg)
                return NodeResult.error(error_msg)

            # Import AutoGen modules
            try:
                # We'll use a try-except block to handle potential import errors
                # The actual import will be done at runtime
                pass
            except Exception as e:
                error_msg = f"Error with AutoGen modules: {str(e)}"
                context.log(error_msg)
                return NodeResult.error(error_msg)

            # Configure the model
            model_config = {
                "model": model_name or "gpt-4-turbo",
                "api_key": api_key,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }

            # Add base_url if provided (for custom endpoints)
            if base_url:
                model_config["base_url"] = base_url

            # Create config list for AutoGen
            config_list = [model_config]

            # Create the LLM configuration
            llm_config = {
                "config_list": config_list,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }

            # Add streaming if enabled
            if stream:
                llm_config["stream"] = True

            # Create agents based on autogen_agent_type
            agents = []

            # Import the required modules from autogen
            import autogen

            # Determine system message
            effective_system_message = system_message or f"You are a helpful AI assistant tasked with: {query}"

            # Create the assistant agent
            assistant = autogen.AssistantAgent(
                name=agent_name or "assistant",
                llm_config=llm_config,
                system_message=effective_system_message,
            )
            agents.append(assistant)

            # Create the user proxy agent
            user_proxy = autogen.UserProxyAgent(
                name="user",
                human_input_mode="NEVER",  # No human input in workflow execution
                max_consecutive_auto_reply=10,
                code_execution_config={"use_docker": False} if autogen_agent_type == "CodeExecutor" else None,
            )
            agents.append(user_proxy)

            # Register tools if provided
            if tools:
                for tool in tools:
                    if hasattr(tool, "function"):
                        user_proxy.register_function(tool.function)

            # Initialize conversation memory if provided
            if memory and hasattr(memory, "messages"):
                # If memory has messages attribute, use it to initialize the conversation
                assistant.messages = memory.messages.copy() if hasattr(memory.messages, "copy") else memory.messages

            # Execute the agent conversation
            context.log("Starting AutoGen agent conversation...")

            # Prepare the initial message with input variables
            initial_message = query
            if input_variables:
                initial_message += "\n\nHere is additional context:\n"
                for key, value in input_variables.items():
                    initial_message += f"{key}: {value}\n"

            # Start the conversation
            chat_result = await user_proxy.a_initiate_chat(
                assistant,
                message=initial_message,
            )

            # Extract the final answer from the last message
            final_answer = chat_result.summary or assistant.last_message()["content"]

            # Extract intermediate steps
            intermediate_steps = []
            for message in assistant.chat_messages.get("user", []):
                intermediate_steps.append({
                    "role": "user",
                    "content": message["content"],
                })

            for message in assistant.chat_messages.get("assistant", []):
                intermediate_steps.append({
                    "role": "assistant",
                    "content": message["content"],
                })

            # Update memory
            updated_memory = memory
            if memory:
                # If memory has messages attribute, update it
                if hasattr(memory, "messages"):
                    memory.messages = assistant.chat_messages
                updated_memory = memory
            else:
                # Create a simple memory object with messages
                updated_memory = {"messages": assistant.chat_messages}

            context.log("AutoGen agent execution completed successfully.")

            # Return success result with outputs
            return NodeResult.success({
                "final_answer": final_answer,
                "intermediate_steps": intermediate_steps,
                "updated_memory": updated_memory,
            })

        except Exception as e:
            error_msg = f"Error executing AutoGen agent: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles dual-purpose inputs, retrieving the value from the context.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input, or the default value if not found.
        """
        # Get the current node ID from the context
        node_id = context.current_node_id
        if not node_id:
            logging.warning("No current node ID in context")
            return default

        # Get all inputs for the current node
        node_inputs = context.node_outputs.get(node_id, {})

        # Check for the input value (dual-purpose inputs use the same name for both connected and direct values)
        input_value = node_inputs.get(input_name)
        if input_value is not None:
            return input_value

        # If not found in context, return the default value
        return default

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the AgenticAI.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values.

        Returns:
            A dictionary with the execution results.
        """
        print(f"WARNING: Using legacy build method for {self.name}. Please update to use execute method.")

        # Return a deprecation message
        return {
            "error": "The build method is deprecated for AgenticAI. This component now uses the modern execute method. Please update your workflow to use the execute method instead.",
            "final_answer": "Legacy build method called - please update to use execute method",
            "intermediate_steps": [],
            "updated_memory": None,
        }